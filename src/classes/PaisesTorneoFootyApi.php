<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a paises_torneos_footy_api record.
 * Assumes global functions ordena(), desordena(), validar_textovacio() are available.
 */
class PaisesTorneoFootyApi
{
	// --- Attributes ---
	private ?string $id       = null; // Stores the 'desordenado' ID
	private ?int    $id_pais  = null;
	private ?string $season   = null;
	private ?int    $id_footy = null;

	/**
	 * Constructor: Initializes properties with default null values.
	 */
	public function __construct()
	{
		$this->id       = null;
		$this->id_pais  = null;
		$this->season   = null;
		$this->id_footy = null;
	}

	/**
	 * Static factory method to create an instance from an array (e.g., DB result).
	 * Assumes $data contains raw column names from the database.
	 *
	 * @param array $data Associative array containing property values.
	 *
	 * @return self Returns a new instance of PaisesTorneoFootyApi.
	 * @throws Exception If 'desordena' function is missing or data is invalid.
	 */
	public static function construct(array $data): self
	{
		if (!function_exists('desordena')) {
			throw new Exception("Global function 'desordena' is required but not found.");
		}

		try {
			$objeto = new self();
			// The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
			$dbId = $data['id'] ?? null;
			if ($dbId !== null) {
				$desordenadoId = desordena((string)$dbId);
				// Add check: Ensure desordena returned a valid, non-empty string ID
				if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
					error_log("desordena() returned empty/null for DB ID: " . $dbId);
					throw new Exception("Error processing PaisesTorneoFootyApi ID during object construction. desordena() failed.");
				}
				$objeto->id = $desordenadoId; // Store the validated desordenado ID
			} else {
				$objeto->id = null; // No ID in data, likely a new object scenario before insert
			}

			$objeto->id_pais  = isset($data['id_pais']) ? (int)$data['id_pais'] : null;
			$objeto->season   = $data['season'] ?? null;
			$objeto->id_footy = isset($data['id_footy']) ? (int)$data['id_footy'] : null;

			return $objeto;

		} catch (Exception $e) {
			// Consider logging the error here
			error_log("Error constructing PaisesTorneoFootyApi from data: " . print_r($data, true) . " Error: " . $e->getMessage());
			throw new Exception("Error constructing PaisesTorneoFootyApi: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves a PaisesTorneoFootyApi object from the database by its 'desordenado' string ID.
	 *
	 * @param string $id       The 'desordenado' string ID of the PaisesTorneoFootyApi to retrieve.
	 * @param PDO    $conexion The database connection object.
	 *
	 * @return self|null A PaisesTorneoFootyApi object if found, null otherwise.
	 * @throws Exception If 'ordena' function is missing or DB error occurs.
	 * @throws InvalidArgumentException If the provided ID is empty or invalid.
	 */
	public static function get(string $id, PDO $conexion): ?self
	{
		if (empty(trim($id))) {
			throw new InvalidArgumentException("Invalid ID provided.");
		}
		if (!function_exists('ordena')) {
			throw new Exception("Global function 'ordena' is required but not found.");
		}

		try {
			// Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
			$idOrdenado = ordena($id);
			if ($idOrdenado === false || $idOrdenado <= 0) {
				throw new InvalidArgumentException("Failed to process the provided ID.");
			}

			$query = <<<SQL
            SELECT *
            FROM paises_torneos_footy_api
            WHERE id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? static::construct($resultado) : null;

		} catch (PDOException $e) {
			error_log("Database error getting PaisesTorneoFootyApi (ID: $id / $idOrdenado): " . $e->getMessage());
			throw new Exception("Database error fetching PaisesTorneoFootyApi: " . $e->getMessage());
		} catch (Exception $e) { // Catch potential errors from ordena or construct
			error_log("Error getting PaisesTorneoFootyApi (ID: $id): " . $e->getMessage());
			throw new Exception("Error fetching PaisesTorneoFootyApi: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves a list of all PaisesTorneoFootyApi objects.
	 *
	 * @param PDO $conexion The database connection object.
	 *
	 * @return array An array of PaisesTorneoFootyApi objects. Returns an empty array if no results.
	 * @throws Exception If there is an error during the database query or object construction.
	 */
	public static function getList(PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT *
            FROM paises_torneos_footy_api
            ORDER BY id_pais ASC, season ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			if ($resultados) {
				foreach ($resultados as $resultado) {
					try {
						$listado[] = static::construct($resultado);
					} catch (Exception $e) {
						error_log("Error constructing PaisesTorneoFootyApi during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
						// Skip this item and log
					}
				}
			}
			return $listado;

		} catch (PDOException $e) {
			error_log("Database error getting PaisesTorneoFootyApi list: " . $e->getMessage());
			throw new Exception("Database error fetching PaisesTorneoFootyApi list: " . $e->getMessage());
		} catch (Exception $e) { // Catch potential errors from construct
			error_log("Error getting PaisesTorneoFootyApi list: " . $e->getMessage());
			throw new Exception("Error fetching PaisesTorneoFootyApi list: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves a list of PaisesTorneoFootyApi objects by country ID.
	 *
	 * @param int $id_pais  The country ID to filter by.
	 * @param PDO $conexion The database connection object.
	 *
	 * @return array An array of PaisesTorneoFootyApi objects. Returns an empty array if no results.
	 * @throws Exception If there is an error during the database query or object construction.
	 */
	public static function getListByPais(int $id_pais, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT *
            FROM paises_torneos_footy_api
            WHERE id_pais = :id_pais
            ORDER BY season ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_pais", $id_pais, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			if ($resultados) {
				foreach ($resultados as $resultado) {
					try {
						$listado[] = static::construct($resultado);
					} catch (Exception $e) {
						error_log("Error constructing PaisesTorneoFootyApi during getListByPais for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
						// Skip this item and log
					}
				}
			}
			return $listado;

		} catch (PDOException $e) {
			error_log("Database error getting PaisesTorneoFootyApi list by pais: " . $e->getMessage());
			throw new Exception("Database error fetching PaisesTorneoFootyApi list by pais: " . $e->getMessage());
		} catch (Exception $e) { // Catch potential errors from construct
			error_log("Error getting PaisesTorneoFootyApi list by pais: " . $e->getMessage());
			throw new Exception("Error fetching PaisesTorneoFootyApi list by pais: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves a PaisesTorneoFootyApi object by country ID and season.
	 *
	 * @param int    $id_pais  The country ID.
	 * @param string $season   The season.
	 * @param PDO    $conexion The database connection object.
	 *
	 * @return self|null A PaisesTorneoFootyApi object if found, null otherwise.
	 * @throws Exception If there is an error during the database query or object construction.
	 */
	public static function getByPaisAndSeason(int $id_pais, string $season, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT *
            FROM paises_torneos_footy_api
            WHERE id_pais = :id_pais AND season = :season
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_pais", $id_pais, PDO::PARAM_INT);
			$statement->bindValue(":season", $season, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? static::construct($resultado) : null;

		} catch (PDOException $e) {
			error_log("Database error getting PaisesTorneoFootyApi by pais and season: " . $e->getMessage());
			throw new Exception("Database error fetching PaisesTorneoFootyApi by pais and season: " . $e->getMessage());
		} catch (Exception $e) { // Catch potential errors from construct
			error_log("Error getting PaisesTorneoFootyApi by pais and season: " . $e->getMessage());
			throw new Exception("Error fetching PaisesTorneoFootyApi by pais and season: " . $e->getMessage());
		}
	}

	/**
	 * Saves (inserts or updates) the current PaisesTorneoFootyApi instance to the database.
	 *
	 * @param PDO $conexion The PDO database connection object.
	 *
	 * @return bool True on success, false on failure.
	 * @throws Exception If validation fails or a database error occurs.
	 */
	public function guardar(PDO $conexion): bool
	{
		// Debugging: Log the ID right before determining the operation type
		$currentId = $this->getId();
		error_log("PaisesTorneoFootyApi::guardar() called. ID is: " . var_export($currentId, true));

		// Determine if it's an insert or update *before* validation
		$isInsertOperation = ($currentId === null || empty(trim($currentId)));
		error_log("PaisesTorneoFootyApi::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

		// Call validation, passing the correct flag
		$this->validarDatos($isInsertOperation); // Pass true for insert, false for update

		try {
			// Use the pre-determined flag for the operation
			if (!$isInsertOperation) { // It's an update
				return $this->_update($conexion);
			} else { // It's an insert
				return $this->_insert($conexion);
			}
		} catch (PDOException $e) {
			$idInfo = $this->getId() ?? 'N/A';
			error_log("Database error saving PaisesTorneoFootyApi (ID: {$idInfo}): " . $e->getMessage());
			throw new Exception("Database error saving PaisesTorneoFootyApi: " . $e->getMessage());
		} catch (Exception $e) {
			error_log("General error saving PaisesTorneoFootyApi: " . $e->getMessage());
			throw new Exception("Error saving PaisesTorneoFootyApi: " . $e->getMessage());
		}
	}

	/**
	 * Inserts the current PaisesTorneoFootyApi instance into the database. (Private method)
	 *
	 * @param PDO $conexion The PDO database connection object.
	 *
	 * @return bool True on success, false on failure.
	 * @throws PDOException On database error.
	 * @throws Exception If 'desordena' function is missing.
	 */
	private function _insert(PDO $conexion): bool
	{
		if (!function_exists('desordena')) {
			throw new Exception("Global function 'desordena' is required but not found.");
		}

		$query = <<<SQL
        INSERT INTO paises_torneos_footy_api (
            id_pais,
            season,
            id_footy
        ) VALUES (
            :id_pais,
            :season,
            :id_footy
        )
        SQL;

		$statement = $conexion->prepare($query);

		$statement->bindValue(':id_pais', $this->getIdPais(), PDO::PARAM_INT);
		$statement->bindValue(':season', $this->getSeason(), PDO::PARAM_STR);
		$statement->bindValue(':id_footy', $this->getIdFooty(), PDO::PARAM_INT);

		$success = $statement->execute();

		if ($success) {
			// Get the last inserted ID (which is the 'ordenado' integer ID)
			$lastIdOrdenado = $conexion->lastInsertId();
			if ($lastIdOrdenado) {
				// Convert it to 'desordenado' string ID and set it on the object
				$this->setId(desordena((string)$lastIdOrdenado));
			} else {
				error_log("Failed to retrieve lastInsertId after PaisesTorneoFootyApi insert.");
				return false;
			}
		} else {
			error_log("Failed to insert PaisesTorneoFootyApi: " . implode(" | ", $statement->errorInfo()));
		}

		return $success;
	}

	/**
	 * Updates the current PaisesTorneoFootyApi instance in the database. (Private method)
	 *
	 * @param PDO $conexion The PDO database connection object.
	 *
	 * @return bool True on success, false on failure.
	 * @throws PDOException On database error.
	 * @throws Exception If 'ordena' function is missing or ID is invalid.
	 */
	private function _update(PDO $conexion): bool
	{
		if ($this->getId() === null || empty(trim($this->getId()))) {
			throw new Exception("Cannot update PaisesTorneoFootyApi without a valid ID.");
		}
		if (!function_exists('ordena')) {
			throw new Exception("Global function 'ordena' is required but not found.");
		}

		// Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
		$idOrdenado = ordena($this->getId());
		if ($idOrdenado === false || $idOrdenado <= 0) {
			throw new Exception("Failed to process the PaisesTorneoFootyApi ID for update: " . $this->getId());
		}

		$query = <<<SQL
        UPDATE paises_torneos_footy_api SET
             id_pais = :id_pais
            ,season = :season
            ,id_footy = :id_footy
        WHERE id = :id
        SQL;

		$statement = $conexion->prepare($query);

		$statement->bindValue(':id_pais', $this->getIdPais(), PDO::PARAM_INT);
		$statement->bindValue(':season', $this->getSeason(), PDO::PARAM_STR);
		$statement->bindValue(':id_footy', $this->getIdFooty(), PDO::PARAM_INT);
		$statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

		$success = $statement->execute();

		if (!$success) {
			error_log("Failed to update PaisesTorneoFootyApi (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
		}

		return $success;
	}

	/**
	 * Deletes a PaisesTorneoFootyApi record from the database.
	 *
	 * @param string $id       The 'desordenado' string ID of the PaisesTorneoFootyApi to delete.
	 * @param PDO    $conexion The database connection object.
	 *
	 * @return bool True on success, false on failure.
	 * @throws Exception If 'ordena' function is missing or DB error occurs.
	 * @throws InvalidArgumentException If the ID is invalid.
	 */
	public static function delete(string $id, PDO $conexion): bool
	{
		if (empty(trim($id))) {
			throw new InvalidArgumentException("Invalid ID provided for deletion.");
		}
		if (!function_exists('ordena')) {
			throw new Exception("Global function 'ordena' is required but not found.");
		}

		try {
			// Convert 'desordenado' string ID to 'ordenado' integer ID
			$idOrdenado = ordena($id);
			if ($idOrdenado === false || $idOrdenado <= 0) {
				throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
			}

			$query = <<<SQL
            DELETE FROM paises_torneos_footy_api
            WHERE id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			error_log("Database error deleting PaisesTorneoFootyApi (ID: $id / $idOrdenado): " . $e->getMessage());
			throw new Exception("Database error deleting PaisesTorneoFootyApi: " . $e->getMessage());
		} catch (Exception $e) { // Catch potential errors from ordena
			error_log("Error deleting PaisesTorneoFootyApi (ID: $id): " . $e->getMessage());
			throw new Exception("Error deleting PaisesTorneoFootyApi: " . $e->getMessage());
		}
	}

	/**
	 * Validates the essential data for the PaisesTorneoFootyApi.
	 * Assumes global validation functions exist.
	 * Throws an Exception if validation fails.
	 *
	 * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
	 *
	 * @throws Exception If validation fails.
	 */
	private function validarDatos(bool $isInsert = false): void
	{
		// Assume global functions exist and handle throwing exceptions on failure
		if (!function_exists('validar_textovacio')) {
			throw new Exception("Required global validation function 'validar_textovacio' is missing.");
		}

		try {
			// Validate id_pais
			if ($this->getIdPais() === null) {
				throw new Exception('El ID del país es requerido');
			}
			if ($this->getIdPais() <= 0) {
				throw new Exception('El ID del país debe ser un número positivo');
			}

			// Validate season
			if ($this->getSeason() !== null) {
				validar_textovacio($this->getSeason(), 'La temporada no puede estar vacía');
			} else {
				throw new Exception('La temporada es requerida');
			}

			// Validate id_footy
			if ($this->getIdFooty() === null) {
				throw new Exception('El ID de Footy es requerido');
			}
			if ($this->getIdFooty() <= 0) {
				throw new Exception('El ID de Footy debe ser un número positivo');
			}

		} catch (Exception $e) {
			// Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
			throw new Exception("Validation failed: " . $e->getMessage());
		}
	}

	// --- GETTERS AND SETTERS ---

	public function getId(): ?string
	{
		return $this->id;
	}

	/**
	 * Sets the 'desordenado' string ID.
	 */
	public function setId(?string $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getIdPais(): ?int
	{
		return $this->id_pais;
	}

	public function setIdPais(?int $id_pais): self
	{
		$this->id_pais = $id_pais;
		return $this;
	}

	public function getSeason(): ?string
	{
		return $this->season;
	}

	public function setSeason(?string $season): self
	{
		$this->season = $season;
		return $this;
	}

	public function getIdFooty(): ?int
	{
		return $this->id_footy;
	}

	public function setIdFooty(?int $id_footy): self
	{
		$this->id_footy = $id_footy;
		return $this;
	}
}
