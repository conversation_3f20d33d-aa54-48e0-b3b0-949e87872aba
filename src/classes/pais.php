<?php


class Pais
{
    public string $id;
    public string $nombre;
    public string $perc_penalidad;
    public int $estado;
    public string $bd_table = 'paises';
    public string $bd_alias = 'pas';
    public string $bd_id = 'id_pais';
    public string $bd_nombre = 'nombre';
    public string $bd_perc_penalidad = 'perc_penalidad';
    public string $bd_estado = 'estado';


    function __construct()
    {
        $this->id = '';
        $this->nombre = 'nombre';
        $this->perc_penalidad = 0;
        $this->estado = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto                 = new self;
            $objeto->id             = desordena($resultado[$cq->bd_id]);
            $objeto->nombre         = $resultado[$cq->bd_nombre];
            $objeto->perc_penalidad = $resultado[$cq->bd_perc_penalidad];
            $objeto->estado         = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_perc_penalidad_bynombre($nombre,PDO $conexion): int|float
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.$cq->bd_perc_penalidad ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_nombre = :$cq->bd_nombre ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $nombre);
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return $resultado[0];

            } else {
                return 0;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getByNombre($nombre, $conexion): string
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_nombre = :$cq->bd_nombre ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $nombre);
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return desordena($resultado[$cq->bd_id]);

            } else {
                return '';
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * @throws Exception
     */
    public static function getList($conexion): array
    {
        try {
            $cq  = new self;

            $query     = self::getListQuery(array());
            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getListFiltered($paramref, $conexion): array
    {
        try {
            $nombre = (isset($paramref['nombre'])) ? $paramref['nombre'] : "";

            $cq = new self;

            $param = array();
            $param['nombre'] = $nombre;
            $query = self::getListQuery($param);
            $statement = $conexion->prepare($query);
            
            //BEGIN - filtros:
            $statement->bindValue(":$cq->bd_estado", 1);

            if(!empty($nombre)){
                $statement->bindValue(":$cq->bd_nombre", '%' . mb_strtoupper($nombre) . '%');
            }
            //END - filtros:

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private static function getListQuery($paramref): string
    {
        try {
            $nombre = (isset($paramref['nombre'])) ? $paramref['nombre'] : "";

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query  = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";

            // FILTROS
            if(!empty($nombre)){
                $query .= "AND $cqa.$cq->bd_nombre LIKE :$cq->bd_nombre ";
            }
            
            $query .= "ORDER BY ";
            $query .= "  $cqa.$cq->bd_nombre ";

            return $query;
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $this->validateData();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_nombre ";
            $query .= "  ,$cq->bd_perc_penalidad ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_nombre ";
            $query .= "  ,:$cq->bd_perc_penalidad ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_perc_penalidad", $this->perc_penalidad);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify($conexion): void
    {
        try {
            $this->validateData();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "  ,$cq->bd_perc_penalidad = :$cq->bd_perc_penalidad ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_perc_penalidad", $this->perc_penalidad);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validateData(): void
    {
        try {
            validar_textovacio($this->nombre, "Debe especificar el nombre.");

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>