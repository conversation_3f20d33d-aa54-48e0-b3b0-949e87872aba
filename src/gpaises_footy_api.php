<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\PaisesTorneoFootyApi;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newPaisesTorneoFootyApi = new PaisesTorneoFootyApi;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El registro ha sido modificado.';
        }
        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'El registro ha sido ingresado.';
        }
        if (isset($_GET['d'])) {
            $success_display = 'show';
            $success_text = 'El registro ha sido eliminado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        // Use setters to set the values
        $newPaisesTorneoFootyApi->setIdPais((int)limpiar_datos($_POST['id_pais']));
        $newPaisesTorneoFootyApi->setSeason(limpiar_datos($_POST['season']));
        $newPaisesTorneoFootyApi->setIdFooty((int)limpiar_datos($_POST['id_footy']));

        // Use guardar method to save
        $newPaisesTorneoFootyApi->guardar($conexion);

        header('Location: gestionar-paises-footy-api?i=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add

#region sub_delete
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delete'])) {
    try {
        $delId = limpiar_datos($_POST['mdl_delete_id']);

        PaisesTorneoFootyApi::delete($delId, $conexion);

        header('Location: gestionar-paises-footy-api?d=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delete

#region try
try {
    // Get all PaisesTorneoFootyApi records
    $paisesTorneoFootyApiList = PaisesTorneoFootyApi::getList($conexion);
    
    // Get all countries for the dropdown
    $paises = Pais::getList($conexion);

    // Create an associative array for easy country name lookup
    $paisesMap = [];
    foreach ($paises as $pais) {
        $paisesMap[ordena($pais->id)] = $pais->nombre;
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/gpaises_footy_api.view.php';

?>
