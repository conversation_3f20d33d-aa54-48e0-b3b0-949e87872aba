<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

$paises = array();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'El pais ha sido modificado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $nombre_search = limpiar_datos($_POST['nombre_search']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_editpais
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editpais'])) {
    try {
        $method_editpais = 1;

        $_SESSION['idpais'] = limpiar_datos($_POST['selidpais']);

        header('Location: epais');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editpais
#region sub_delpais
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpais'])) {
    try {
        $method_sub_delpais = 1;
        
        $mdlidpais = limpiar_datos($_POST['mdl_delpais_idpais']);

        Pais::delete($mdlidpais, $conexion);

        $success_display = 'show';
        $success_text = 'El pais ha sido eliminado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpais
#region try
try {
    $method_try = 1;
    
    $paises = Pais::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpaises.view.php';

?>






