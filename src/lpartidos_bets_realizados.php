<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\PartidoBet;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        // Handle any GET parameters if needed
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'Operación completada exitosamente.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region post
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Handle date filter with session storage
        if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
            // Store the selected date in session for persistence
            $_SESSION['lpartidos_bets_realizados_fecha'] = $_POST['fecha'];
        }

        // Handle clear date filter request
        if (isset($_POST['clear_date_filter'])) {
            // Clear the session date filter
            unset($_SESSION['lpartidos_bets_realizados_fecha']);
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion post

#region try
try {
    // Set timezone to Bogotá for date filtering
    date_default_timezone_set('America/Bogota');

    // Prepare filters for data retrieval
    $filters = [];

    // Check for date filter with session persistence
    if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
        $filters['fecha'] = $_POST['fecha'];
    } elseif (isset($_SESSION['lpartidos_bets_realizados_fecha']) && !empty($_SESSION['lpartidos_bets_realizados_fecha'])) {
        // Use stored session date if available
        $filters['fecha'] = $_SESSION['lpartidos_bets_realizados_fecha'];
    }
    // If no date filter is set, show all records (don't set fecha in filters)

    // Always filter by active records
    $filters['estado'] = 1;

    // Retrieve betting history data
    $partidosBetsRealizados = PartidoBet::getListForBetsRealizados($conexion, $filters);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidos_bets_realizados.view.php';

?>
