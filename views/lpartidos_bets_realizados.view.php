<?php
#region region DOCS
/** @var array $partidosBetsRealizados */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Historial de Apuestas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
	
	
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <link href="<?php echo RUTA ?>resources/css/grouped_controls.css" rel="stylesheet"/>

    <style>
        .criteria-expand-animation {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .bet-row:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .toggle-details {
            transition: all 0.2s ease;
        }

        .toggle-details:hover {
            transform: scale(1.1);
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .status-closed {
            background-color: #28a745;
            color: white;
        }

        .status-open {
            background-color: #dc3545;
            color: white;
        }

        .profit-positive {
            color: #28a745;
            font-weight: bold;
        }

        .profit-negative {
            color: #dc3545;
            font-weight: bold;
        }

        .profit-zero {
            color: #6c757d;
        }

        .sortable-header {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable-header:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sortable-header::after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.5;
            font-size: 12px;
        }

        .sortable-header.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sortable-header.desc::after {
            content: '↓';
            opacity: 1;
        }

        .card-sm {
            border-radius: 6px;
        }

        .card-sm .card-body {
            padding: 0.5rem;
        }

        .match-info {
            font-size: 0.9rem;
        }

        .bet-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .odds-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
        }

        .details-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
            border-left: 4px solid #007bff;
        }

        .detail-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease;
        }

        .detail-card:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-1px);
        }

        .country-flag {
            width: 20px;
            height: 15px;
            border-radius: 2px;
            margin-right: 8px;
        }

        .amount-display {
            font-weight: bold;
        }

        .date-filter-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .close-bet-btn, .delete-bet-btn {
            transition: all 0.2s ease;
        }

        .close-bet-btn:hover, .delete-bet-btn:hover {
            transform: scale(1.1);
        }

        .btn-group .btn {
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        /* Delete modal styling */
        #deleteBetModal .modal-body {
            padding: 2rem;
        }

        #deleteBetModal .fa-exclamation-triangle {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Custom radio button styling for the modal */
        #closeBetModal .btn-group-toggle .btn {
            border: 2px solid;
            transition: all 0.3s ease;
        }

        #closeBetModal .btn-check:checked + .btn-outline-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }

        #closeBetModal .btn-check:checked + .btn-outline-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>

<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="page-header mb-0">Historial de Apuestas Realizadas</h1>
            <div>
                <a href="partidos-bets" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-plus me-1"></i>Nueva Apuesta
                </a>
                <a href="lpartidos" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-list me-1"></i>Ver Partidos
                </a>
            </div>
        </div>
        <!-- END page-header -->

        <!-- BEGIN date filter section -->
        <div class="date-filter-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        <label for="fechaFilter" class="form-label mb-0 me-2">Filtrar por fecha:</label>
                        <div class="input-group" style="width: auto;">
                            <input type="text" id="fechaFilter" name="fechaFilter"
                                   value="<?php
                                   // Use session stored date if available, otherwise use POST parameter, otherwise empty
                                   if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
                                       echo $_POST['fecha'];
                                   } elseif (isset($_SESSION['lpartidos_bets_realizados_fecha']) && !empty($_SESSION['lpartidos_bets_realizados_fecha'])) {
                                       echo $_SESSION['lpartidos_bets_realizados_fecha'];
                                   }
                                   ?>"
                                   class="form-control form-control-sm datepicker" autocomplete="off" style="width: 150px;"/>
                            <span class="input-group-text">
                                <i class="fa fa-calendar-alt"></i>
                            </span>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm ms-2" id="applyDateFilter">
                            <i class="fas fa-search me-1"></i>Aplicar
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm ms-1" id="clearDateFilter">
                            <i class="fas fa-times me-1"></i>Mostrar Todas
                        </button>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        <?php
                        // Get the current filter date for display
                        $displayDate = '';
                        if (isset($_POST['fecha']) && !empty($_POST['fecha'])) {
                            $displayDate = $_POST['fecha'];
                        } elseif (isset($_SESSION['lpartidos_bets_realizados_fecha']) && !empty($_SESSION['lpartidos_bets_realizados_fecha'])) {
                            $displayDate = $_SESSION['lpartidos_bets_realizados_fecha'];
                        }

                        if (!empty($displayDate)) {
                            echo "Mostrando apuestas del: <strong>$displayDate</strong>";
                        } else {
                            echo "Mostrando <strong>todas las apuestas</strong>";
                        }
                        ?>
                    </small>
                </div>
            </div>
        </div>
        <!-- END date filter section -->

        <!-- BEGIN panel -->
        <div class="panel panel-inverse">
            <!-- BEGIN panel-heading -->
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fas fa-history me-2"></i>Apuestas Realizadas
                    <?php if (!empty($partidosBetsRealizados)): ?>
                        <span class="badge bg-primary ms-2 fs-11px"><?php echo count($partidosBetsRealizados); ?></span>
                    <?php endif; ?>
                </h4>
                <div class="panel-heading-btn">
                    <?php if (!empty($partidosBetsRealizados)): ?>
                        <button type="button" class="btn btn-xs btn-outline-primary me-1" id="toggleAllDetails">
                            <i class="fas fa-expand-alt me-1"></i>Mostrar Todos los Detalles
                        </button>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END panel-heading -->

            <!-- BEGIN panel-body -->
            <div class="panel-body">
                <?php if (empty($partidosBetsRealizados)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No hay apuestas realizadas para la fecha seleccionada.
                        <a href="partidos-bets" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fas fa-plus me-1"></i>Crear Nueva Apuesta
                        </a>
                    </div>
                <?php else: ?>
                    <!-- BEGIN search and filter controls -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Buscar por equipos, tipo de apuesta...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterByStatus">
                                <option value="">Todos los estados</option>
                                <option value="cerrado">Solo cerradas</option>
                                <option value="abierto">Solo abiertas</option>
                                <option value="ganado">Solo ganadas</option>
                                <option value="perdido">Solo perdidas</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="clearFilters">
                                <i class="fas fa-times me-1"></i>Limpiar Filtros
                            </button>
                        </div>
                    </div>
                    <!-- END search and filter controls -->

                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="betsHistoryTable">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 120px;">Acciones</th>
                                    <th class="text-center" style="width: 50px;"></th>
                                    <th class="text-center sortable-header" data-column="2">Fecha Creación</th>
                                    <th class="text-center sortable-header" data-column="3">Valor Apostado</th>
                                    <th class="text-center sortable-header" data-column="4">Cuota</th>
                                    <th class="text-center sortable-header" data-column="5">Estado</th>
                                    <th class="text-center sortable-header" data-column="6">Resultado</th>
                                    <th class="text-center sortable-header" data-column="7">Valor Recibido</th>
                                    <th class="text-center sortable-header" data-column="8">Profit</th>
                                </tr>
                            </thead>
                            <tbody class="fs-13px">
                                <?php foreach ($partidosBetsRealizados as $index => $bet): ?>
                                    <?php
                                    // Set timezone to Bogotá for date formatting
                                    date_default_timezone_set('America/Bogota');

                                    // Use database values directly instead of calculated values
                                    $valorRecibidoFromDB = (float)$bet['valor_recibido'];
                                    $valorProfitFromDB = (float)$bet['valor_profit'];
                                    ?>
                                    <tr class="bet-row" data-bet-index="<?php echo $index; ?>" data-bet-id="<?php echo htmlspecialchars($bet['pb_id']); ?>">
                                        <td class="text-center align-middle">
                                            <div class="btn-group" role="group">
                                                <?php if ($bet['es_cerrado'] == 0): ?>
                                                    <button class="btn btn-sm btn-warning close-bet-btn" type="button"
                                                            data-bet-id="<?php echo htmlspecialchars($bet['pb_id']); ?>"
                                                            data-bs-toggle="modal" data-bs-target="#closeBetModal"
                                                            title="Cerrar Apuesta">
                                                        <i class="fas fa-times-circle"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="btn btn-sm btn-outline-secondary disabled">
                                                        <i class="fas fa-check-circle"></i>
                                                    </span>
                                                <?php endif; ?>
                                                <button class="btn btn-sm btn-danger delete-bet-btn" type="button"
                                                        data-bet-id="<?php echo htmlspecialchars($bet['pb_id']); ?>"
                                                        data-bs-toggle="modal" data-bs-target="#deleteBetModal"
                                                        title="Eliminar Apuesta">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="text-center align-middle">
                                            <button class="btn btn-sm btn-outline-primary toggle-details" type="button" data-bet-index="<?php echo $index; ?>">
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['fecha_creado'])): ?>
                                                <?php
                                                $fechaCreado = new DateTime($bet['fecha_creado']);
                                                $fechaCreado->setTimezone(new DateTimeZone('America/Bogota'));
                                                ?>
                                                <div class="fw-bold"><?php echo $fechaCreado->format('Y-m-d'); ?></div>
                                                <small class="text-muted"><?php echo $fechaCreado->format('H:i A'); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-end align-middle">
                                            <?php if (!empty($bet['valor_apostado'])): ?>
                                                <span class="amount-display text-info">
                                                    $<?php echo number_format((float)$bet['valor_apostado'], 0); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if (!empty($bet['pb_cuota'])): ?>
                                                <?php
                                                $cuota = (float)$bet['pb_cuota'];
                                                $badgeClass = 'bg-primary';
                                                if ($cuota >= 3.0) $badgeClass = 'bg-danger';
                                                elseif ($cuota >= 2.0) $badgeClass = 'bg-warning';
                                                elseif ($cuota >= 1.5) $badgeClass = 'bg-success';
                                                ?>
                                                <span class="badge <?php echo $badgeClass; ?> odds-badge"><?php echo number_format($cuota, 2); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if ($bet['es_cerrado'] == 1): ?>
                                                <span class="status-indicator status-closed">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                                <small class="d-block text-muted">Cerrada</small>
                                            <?php else: ?>
                                                <span class="status-indicator status-open">
                                                    <i class="fas fa-times"></i>
                                                </span>
                                                <small class="d-block text-muted">Abierta</small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center align-middle">
                                            <?php if ($bet['es_cerrado'] == 1): ?>
                                                <?php if ($bet['es_ganado'] == 1): ?>
                                                    <span class="status-indicator status-closed">
                                                        <i class="fas fa-check"></i>
                                                    </span>
                                                    <small class="d-block text-success">Ganada</small>
                                                <?php else: ?>
                                                    <span class="status-indicator status-open">
                                                        <i class="fas fa-times"></i>
                                                    </span>
                                                    <small class="d-block text-danger">Perdida</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-clock"></i>
                                                    <small class="d-block">Pendiente</small>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-end align-middle">
                                            <span class="amount-display <?php echo $valorRecibidoFromDB > 0 ? 'text-success' : 'text-muted'; ?>">
                                                $<?php echo number_format($valorRecibidoFromDB, 0); ?>
                                            </span>
                                        </td>
                                        <td class="text-end align-middle">
                                            <?php
                                            $profitClass = 'profit-zero';
                                            if ($valorProfitFromDB > 0) $profitClass = 'profit-positive';
                                            elseif ($valorProfitFromDB < 0) $profitClass = 'profit-negative';
                                            ?>
                                            <span class="amount-display <?php echo $profitClass; ?>">
                                                <?php echo $valorProfitFromDB >= 0 ? '+' : ''; ?>$<?php echo number_format($valorProfitFromDB, 0); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <!-- Expandable details row -->
                                    <tr class="details-row" id="details-<?php echo $index; ?>" style="display: none;">
                                        <td colspan="9" class="details-section">
                                            <div class="p-3">
                                                <h6 class="mb-3">
                                                    <i class="fas fa-list-ul me-2"></i>Detalles de la Apuesta
                                                    <?php if (!empty($bet['detalles'])): ?>
                                                        <span class="badge bg-secondary ms-2"><?php echo count($bet['detalles']); ?> apuestas</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <?php if (!empty($bet['detalles'])): ?>
                                                    <div class="row">
                                                        <?php foreach ($bet['detalles'] as $detalle): ?>
                                                            <div class="col-md-6 col-lg-4 mb-3">
                                                                <div class="card detail-card">
                                                                    <div class="card-body">
                                                                        <!-- Match Information -->
                                                                        <div class="match-info mb-2">
                                                                            <div class="d-flex align-items-center mb-1">
                                                                                <i class="fas fa-futbol text-primary me-2"></i>
                                                                                <strong class="text-white">
                                                                                    <?php echo htmlspecialchars($detalle['home']); ?> vs <?php echo htmlspecialchars($detalle['away']); ?>
                                                                                </strong>
                                                                            </div>
                                                                            <?php if (!empty($detalle['fecha_partido'])): ?>
                                                                                <div class="d-flex align-items-center mb-1">
                                                                                    <i class="fas fa-calendar text-muted me-2"></i>
                                                                                    <small class="text-muted">
                                                                                        <?php
                                                                                        $fechaPartido = new DateTime($detalle['fecha_partido']);
                                                                                        $fechaPartido->setTimezone(new DateTimeZone('America/Bogota'));
                                                                                        echo $fechaPartido->format('Y-m-d H:i A');
                                                                                        ?>
                                                                                    </small>
                                                                                </div>
                                                                            <?php endif; ?>
                                                                            <?php if (!empty($detalle['pais'])): ?>
                                                                                <div class="d-flex align-items-center mb-2">
                                                                                    <i class="fas fa-globe text-muted me-2"></i>
                                                                                    <small class="text-muted"><?php echo htmlspecialchars($detalle['pais']); ?></small>
                                                                                </div>
                                                                            <?php endif; ?>
                                                                        </div>

                                                                        <!-- Bet Type and Odds -->
                                                                        <div class="d-flex justify-content-between align-items-center">
                                                                            <div>
                                                                                <?php if (!empty($detalle['tipo_apuesta'])): ?>
                                                                                    <span class="badge bg-info bet-type-badge">
                                                                                        <?php echo htmlspecialchars($detalle['tipo_apuesta']); ?>
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                            <div>
                                                                                <?php if (!empty($detalle['cuota'])): ?>
                                                                                    <?php
                                                                                    $detalleCuota = (float)$detalle['cuota'];
                                                                                    $detalleBadgeClass = 'bg-primary';
                                                                                    if ($detalleCuota >= 3.0) $detalleBadgeClass = 'bg-danger';
                                                                                    elseif ($detalleCuota >= 2.0) $detalleBadgeClass = 'bg-warning';
                                                                                    elseif ($detalleCuota >= 1.5) $detalleBadgeClass = 'bg-success';
                                                                                    ?>
                                                                                    <span class="badge <?php echo $detalleBadgeClass; ?> odds-badge">
                                                                                        <?php echo number_format($detalleCuota, 2); ?>
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="alert alert-info mb-0">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        Esta apuesta no tiene detalles asociados.
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            <!-- END panel-body -->
        </div>
        <!-- END panel -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Currency formatting script -->
<script src="resources/js/formatcurrency2.js"></script>

<!-- Datepicker scripts -->
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>

<!-- Grouped controls script -->
<script src="<?php echo RUTA ?>resources/js/grouped_controls.js"></script>

<!-- SweetAlert script -->
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>

<!-- BEGIN JavaScript functionality -->
<script>
$(document).ready(function() {
    // Set timezone to Bogotá
    const bogotaTimezone = 'America/Bogota';

    // Initialize datepicker
    $('#fechaFilter').datepicker({
        format: "yyyy-mm-dd",
        todayHighlight: true,
        autoclose: true,
        orientation: "bottom"
    }).on('changeDate', function(e) {
        // Auto-submit when date is changed
        $('#applyDateFilter').click();
    });

    // Handle expand/collapse functionality
    $('.toggle-details').on('click', function() {
        const betIndex = $(this).data('bet-index');
        const detailsRow = $('#details-' + betIndex);
        const icon = $(this).find('i');
        const button = $(this);

        if (detailsRow.is(':visible')) {
            // Collapse with animation
            detailsRow.slideUp(300, function() {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                button.removeClass('btn-primary').addClass('btn-outline-primary');
            });
        } else {
            // Expand with animation
            detailsRow.slideDown(300);
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            button.removeClass('btn-outline-primary').addClass('btn-primary');

            // Add animation class to the content
            detailsRow.find('.p-3').addClass('criteria-expand-animation');
            setTimeout(() => {
                detailsRow.find('.p-3').removeClass('criteria-expand-animation');
            }, 300);
        }
    });

    // Toggle all details functionality
    let allExpanded = false;
    $('#toggleAllDetails').on('click', function() {
        const button = $(this);
        const icon = button.find('i');

        if (!allExpanded) {
            // Expand all
            $('.toggle-details').each(function() {
                const betIndex = $(this).data('bet-index');
                const detailsRow = $('#details-' + betIndex);
                const rowIcon = $(this).find('i');

                if (!detailsRow.is(':visible')) {
                    detailsRow.slideDown(200);
                    rowIcon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $(this).removeClass('btn-outline-primary').addClass('btn-primary');
                }
            });

            button.html('<i class="fas fa-compress-alt me-1"></i>Ocultar Todos los Detalles');
            allExpanded = true;
        } else {
            // Collapse all
            $('.toggle-details').each(function() {
                const betIndex = $(this).data('bet-index');
                const detailsRow = $('#details-' + betIndex);
                const rowIcon = $(this).find('i');

                if (detailsRow.is(':visible')) {
                    detailsRow.slideUp(200);
                    rowIcon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $(this).removeClass('btn-primary').addClass('btn-outline-primary');
                }
            });

            button.html('<i class="fas fa-expand-alt me-1"></i>Mostrar Todos los Detalles');
            allExpanded = false;
        }
    });

    // Date filter functionality using POST
    $('#applyDateFilter').on('click', function() {
        const selectedDate = $('#fechaFilter').val();
        if (selectedDate) {
            // Create a form and submit it via POST
            const form = $('<form>', {
                'method': 'POST',
                'action': window.location.pathname
            });
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'fecha',
                'value': selectedDate
            }));
            $('body').append(form);
            form.submit();
        }
    });

    // Allow Enter key to apply date filter
    $('#fechaFilter').on('keypress', function(e) {
        if (e.which === 13) {
            $('#applyDateFilter').click();
        }
    });

    // Clear date filter functionality
    $('#clearDateFilter').on('click', function() {
        // Clear the session and reload without date filter
        $.ajax({
            url: window.location.pathname,
            method: 'POST',
            data: {
                clear_date_filter: true
            },
            success: function() {
                // Reload the page without date filter
                window.location.href = window.location.pathname;
            },
            error: function() {
                // Fallback: just reload the page
                window.location.href = window.location.pathname;
            }
        });
    });

    // Search and filter functionality
    function filterTable() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const statusFilter = $('#filterByStatus').val();

        $('#betsHistoryTable tbody tr.bet-row').each(function() {
            const row = $(this);
            const betIndex = row.data('bet-index');
            const detailsRow = $('#details-' + betIndex);

            // Get row data for filtering
            const matchText = row.find('td').eq(2).text().toLowerCase(); // Date column
            const statusText = row.find('td').eq(5).text().toLowerCase(); // Status column
            const resultText = row.find('td').eq(6).text().toLowerCase(); // Result column

            // Check search term (search in all visible text)
            const allRowText = row.text().toLowerCase();
            const matchesSearch = searchTerm === '' || allRowText.includes(searchTerm);

            // Check status filter
            let matchesStatus = true;
            if (statusFilter !== '') {
                switch (statusFilter) {
                    case 'cerrado':
                        matchesStatus = statusText.includes('cerrada');
                        break;
                    case 'abierto':
                        matchesStatus = statusText.includes('abierta');
                        break;
                    case 'ganado':
                        matchesStatus = resultText.includes('ganada');
                        break;
                    case 'perdido':
                        matchesStatus = resultText.includes('perdida');
                        break;
                }
            }

            // Show/hide row and its details
            if (matchesSearch && matchesStatus) {
                row.show();
                // Keep details row hidden unless it was previously expanded
                if (!detailsRow.is(':visible')) {
                    detailsRow.hide();
                }
            } else {
                row.hide();
                detailsRow.hide();
            }
        });

        // Update visible row count
        updateRowCount();
    }

    function updateRowCount() {
        const visibleRows = $('#betsHistoryTable tbody tr.bet-row:visible').length;
        const totalRows = $('#betsHistoryTable tbody tr.bet-row').length;

        // Update or create row count display
        let countDisplay = $('#rowCount');
        if (countDisplay.length === 0) {
            countDisplay = $('<small id="rowCount" class="text-muted ms-2"></small>');
            $('.table-responsive').before(countDisplay);
        }

        // Show/hide no results message
        let noResultsMsg = $('#noResultsMessage');
        if (visibleRows === 0) {
            if (noResultsMsg.length === 0) {
                noResultsMsg = $(`
                    <div id="noResultsMessage" class="alert alert-warning text-center">
                        <i class="fas fa-search me-2"></i>
                        No se encontraron apuestas que coincidan con los filtros aplicados.
                        <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="$('#clearFilters').click()">
                            Limpiar filtros
                        </button>
                    </div>
                `);
                $('.table-responsive').before(noResultsMsg);
            }
            noResultsMsg.show();
            countDisplay.text(`0 de ${totalRows} apuestas`);
        } else {
            noResultsMsg.hide();
            if (visibleRows === totalRows) {
                countDisplay.text(`Mostrando ${totalRows} apuestas`);
            } else {
                countDisplay.text(`Mostrando ${visibleRows} de ${totalRows} apuestas`);
            }
        }
    }

    // Bind search and filter events
    $('#searchInput').on('input', function() {
        filterTable();
    });

    $('#filterByStatus').on('change', function() {
        filterTable();
    });

    // Clear filters functionality
    $('#clearFilters').on('click', function() {
        $('#searchInput').val('');
        $('#filterByStatus').val('');
        filterTable();
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl+E to expand/collapse all
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            $('#toggleAllDetails').click();
        }
        // Ctrl+F to focus search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            $('#searchInput').focus();
        }
    });

    // Table sorting functionality
    $('.sortable-header').on('click', function() {
        const column = $(this).data('column');
        const table = $('#betsHistoryTable');
        const tbody = table.find('tbody');
        const rows = tbody.find('tr.bet-row').get();

        // Determine sort direction
        const isAsc = $(this).hasClass('asc');
        $('.sortable-header').removeClass('asc desc');
        $(this).addClass(isAsc ? 'desc' : 'asc');

        // Sort rows
        rows.sort((a, b) => {
            const aVal = $(a).find('td').eq(column).text().trim();
            const bVal = $(b).find('td').eq(column).text().trim();

            // Try to parse as numbers first
            const aNum = parseFloat(aVal.replace(/[$,]/g, ''));
            const bNum = parseFloat(bVal.replace(/[$,]/g, ''));

            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAsc ? bNum - aNum : aNum - bNum;
            }

            // Fall back to string comparison
            return isAsc ? bVal.localeCompare(aVal) : aVal.localeCompare(bVal);
        });

        // Reorder rows in DOM
        rows.forEach((row, index) => {
            const betIndex = $(row).data('bet-index');
            const detailsRow = $('#details-' + betIndex);
            tbody.append(row);
            tbody.append(detailsRow);
        });
    });

    // Initialize row count
    updateRowCount();



    // Tooltip initialization for status indicators
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Add hover effects for better UX
    $('.bet-row').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );

    // Print functionality
    window.printBetsHistory = function() {
        const printWindow = window.open('', '_blank');
        const printContent = `
            <html>
            <head>
                <title>Historial de Apuestas - ${new Date().toLocaleDateString()}</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; }
                    .profit-positive { color: green; font-weight: bold; }
                    .profit-negative { color: red; font-weight: bold; }
                </style>
            </head>
            <body>
                <h2>Historial de Apuestas Realizadas</h2>
                <p>Fecha: ${new Date().toLocaleDateString()}</p>
                ${$('#betsHistoryTable').prop('outerHTML')}
            </body>
            </html>
        `;
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    };

    // Close bet modal functionality
    $('#closeBetModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const betId = button.data('bet-id');

        // Set the bet ID in the hidden input
        $('#closeBetId').val(betId);

        // Reset radio buttons and confirm button
        $('input[name="betResult"]').prop('checked', false);
        $('.btn-check').each(function() {
            $(this).next('label').removeClass('active');
        });
        $('#confirmCloseBet').prop('disabled', true);
    });

    // Handle radio button selection
    $('input[name="betResult"]').on('change', function() {
        $('#confirmCloseBet').prop('disabled', false);
    });

    // Handle confirm close bet
    $('#confirmCloseBet').on('click', function() {
        const betId = $('#closeBetId').val();
        const result = $('input[name="betResult"]:checked').val();

        if (!betId || !result) {
            alert('Error: Datos incompletos');
            return;
        }

        // Disable button to prevent double-click
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Procesando...');

        // Send AJAX request
        $.ajax({
            url: 'src/ajax_close_bet.php',
            method: 'POST',
            data: {
                bet_id: betId,
                result: result
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Hide modal
                    $('#closeBetModal').modal('hide');

                    // Show SweetAlert success message
                    const resultText = response.result === 'won' ? 'ganada' : 'perdida';
                    swal({
                        text: `Apuesta cerrada exitosamente como ${resultText}`,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-success',
                                closeModal: true
                            }
                        }
                    });

                    // Update the row to show closed status instead of removing it
                    const row = $('tr[data-bet-id="' + betId + '"]');

                    // Update the action column to show closed status
                    const actionCell = row.find('td').first();
                    actionCell.html('<span class="text-muted"><i class="fas fa-check-circle"></i></span>');

                    // Update the status column (6th column - index 5)
                    const statusCell = row.find('td').eq(5);
                    statusCell.html(`
                        <span class="status-indicator status-closed">
                            <i class="fas fa-check"></i>
                        </span>
                        <small class="d-block text-muted">Cerrada</small>
                    `);

                    // Update the result column (7th column - index 6)
                    const resultCell = row.find('td').eq(6);
                    if (response.result === 'won') {
                        resultCell.html(`
                            <span class="status-indicator status-closed">
                                <i class="fas fa-check"></i>
                            </span>
                            <small class="d-block text-success">Ganada</small>
                        `);
                    } else {
                        resultCell.html(`
                            <span class="status-indicator status-open">
                                <i class="fas fa-times"></i>
                            </span>
                            <small class="d-block text-danger">Perdida</small>
                        `);
                    }

                    // Add a subtle highlight effect to show the row was updated
                    row.addClass('table-success');
                    setTimeout(function() {
                        row.removeClass('table-success');
                    }, 2000);
                } else {
                    // Show SweetAlert error message
                    swal({
                        text: response.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Error al cerrar la apuesta';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                // Show SweetAlert error message
                swal({
                    text: errorMessage,
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            },
            complete: function() {
                // Re-enable button
                $('#confirmCloseBet').prop('disabled', false).html('<i class="fas fa-check me-1"></i>Confirmar');
            }
        });
    });

    // Delete bet modal functionality
    $('#deleteBetModal').on('show.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const betId = button.data('bet-id');

        // Set the bet ID in the hidden input
        $('#deleteBetId').val(betId);
    });

    // Handle confirm delete bet
    $('#confirmDeleteBet').on('click', function() {
        const betId = $('#deleteBetId').val();

        if (!betId) {
            swal({
                text: 'Error: ID de apuesta no encontrado',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
            return;
        }

        // Disable button to prevent double-click
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Eliminando...');

        // Send AJAX request
        $.ajax({
            url: 'src/ajax_delete_bet.php',
            method: 'POST',
            data: {
                bet_id: betId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Hide modal
                    $('#deleteBetModal').modal('hide');

                    // Show SweetAlert success message
                    swal({
                        text: response.message,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-success',
                                closeModal: true
                            }
                        }
                    });

                    // Remove the row from the table with fade-out animation
                    const row = $('tr[data-bet-id="' + betId + '"]');
                    const detailsRow = row.next('.details-row');

                    // Fade out and remove both rows
                    row.fadeOut(300, function() {
                        $(this).remove();
                        // Update row count if function exists
                        if (typeof updateRowCount === 'function') {
                            updateRowCount();
                        }
                    });
                    detailsRow.fadeOut(300, function() {
                        $(this).remove();
                    });

                } else {
                    // Show SweetAlert error message
                    swal({
                        text: response.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Error al eliminar la apuesta';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                // Show SweetAlert error message
                swal({
                    text: errorMessage,
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            },
            complete: function() {
                // Re-enable button
                $('#confirmDeleteBet').prop('disabled', false).html('<i class="fas fa-trash me-1"></i>Confirmar');
            }
        });
    });

    console.log('Betting history page initialized successfully');
});
</script>

<!-- Close Bet Modal -->
<div class="modal fade" id="closeBetModal" tabindex="-1" aria-labelledby="closeBetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="closeBetModalLabel">
                    <i class="fas fa-times-circle me-2"></i>Cerrar Apuesta
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">¿Cuál fue el resultado de esta apuesta?</p>

                <div class="btn-group btn-group-toggle w-100" data-bs-toggle="buttons">
                    <input type="radio" class="btn-check" name="betResult" id="betResultWon" value="won" autocomplete="off">
                    <label class="btn btn-outline-success" for="betResultWon">
                        <i class="fas fa-trophy me-1"></i>Ganada
                    </label>

                    <input type="radio" class="btn-check" name="betResult" id="betResultLost" value="lost" autocomplete="off">
                    <label class="btn btn-outline-danger" for="betResultLost">
                        <i class="fas fa-times me-1"></i>Perdida
                    </label>
                </div>

                <input type="hidden" id="closeBetId" name="closeBetId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-arrow-left me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-primary" id="confirmCloseBet" disabled>
                    <i class="fas fa-check me-1"></i>Confirmar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Bet Modal -->
<div class="modal fade" id="deleteBetModal" tabindex="-1" aria-labelledby="deleteBetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteBetModalLabel">
                    <i class="fas fa-trash me-2"></i>Eliminar Apuesta
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <p class="mb-3">¿Está seguro que desea eliminar esta apuesta?</p>
                    <p class="text-muted small">Esta acción no se puede deshacer.</p>
                </div>

                <input type="hidden" id="deleteBetId" name="deleteBetId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancelar
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBet">
                    <i class="fas fa-trash me-1"></i>Confirmar
                </button>
            </div>
        </div>
    </div>
</div>

</body>
</html>
