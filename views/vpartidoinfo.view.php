<?php
#region region DOCS
/** @var Partido $modpartido */
/** @var PartidoApuesta[] $partidosapuestas */
/** @var Pais $paises */
/** @var PartidoTorneo[] $partidotorneos */
/** @var array $rank_goals */
/** @var array $infobtts */
/** @var array $criterios_hvsa */
/** @var array $info_hvsa */
/** @var array $percentage_exito */
/** @var array $infogoleshome */
/** @var array $infogolesaway */
/** @var array $posession */
/** @var array $infocornershome */
/** @var string $home_maxfecha */
/** @var string $away_maxfecha */
/** @var int $count_partidos_mismotorneo_porrevisar */
/** @var array $riesgo_w_penalidad */
/** @var Config $current_config */
/** @var PaisSeason $pais_season */
/** @var ApuestaTipo[] $apuestastipos */
/** @var array $infocornersaway */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region FORM ?>
        <form action="vpartidoinfo" method="POST">
            <input type="hidden" id="idpartido" name="idpartido" value="<?php echo @recover_var($idpartido) ?>">
            <input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">
            <input type="hidden" id="totalcorners_masde" name="totalcorners_masde" value="<?php echo @recover_var($totalcorners_masde) ?>">
            <input type="hidden" id="totalcorners_menosde" name="totalcorners_menosde" value="<?php echo @recover_var($totalcorners_menosde) ?>">
            <input type="hidden" id="cornershome_masde" name="cornershome_masde" value="<?php echo @recover_var($cornershome_masde) ?>">
            <input type="hidden" id="cornershome_menosde" name="cornershome_menosde" value="<?php echo @recover_var($cornershome_menosde) ?>">
            <input type="hidden" id="cornersaway_masde" name="cornersaway_masde" value="<?php echo @recover_var($cornersaway_masde) ?>">
            <input type="hidden" id="cornersaway_menosde" name="cornersaway_menosde" value="<?php echo @recover_var($cornersaway_menosde) ?>">
            <input type="hidden" id="selidpartidoapuesta" name="selidpartidoapuesta">
            <input type="hidden" id="selidpais" name="selidpais">
            <input type="hidden" id="selidpartidotorneo" name="selidpartidotorneo">
            <input type="hidden" id="totalgoles_masde" name="totalgoles_masde" value="<?php echo @recover_var($totalgoles_masde) ?>">
            <input type="hidden" id="totalgoles_menosde" name="totalgoles_menosde" value="<?php echo @recover_var($totalgoles_menosde) ?>">
            <input type="hidden" id="goleshome_masde" name="goleshome_masde" value="<?php echo @recover_var($goleshome_masde) ?>">
            <input type="hidden" id="goleshome_menosde" name="goleshome_menosde" value="<?php echo @recover_var($goleshome_menosde) ?>">
            <input type="hidden" id="golesaway_masde" name="golesaway_masde" value="<?php echo @recover_var($golesaway_masde) ?>">
            <input type="hidden" id="golesaway_menosde" name="golesaway_menosde" value="<?php echo @recover_var($golesaway_menosde) ?>">
            <input type="hidden" id="apuesta_tipo_id_apuesta_rapida" name="apuesta_tipo_id_apuesta_rapida">
            <input type="hidden" id="valor_apuesta_apuesta_rapida" name="valor_apuesta_apuesta_rapida">

            <!-- BEGIN SUBMIT editpartidoapuesta -->
            <div class="col" style="display: none">
                <button type="submit" id="sub_editpartidoapuesta" name="sub_editpartidoapuesta" class="region_SUBMIT_editpartidoapuesta btn btn-success w-100">
                    sub_editpartidoapuesta
                </button>
            </div>
            <!-- END SUBMIT editpartidoapuesta -->
            <!-- BEGIN SUBMIT sub_addtorneo_fromtable -->
            <div class="col" style="display: none">
                <button type="submit" id="sub_addtorneo_fromtable" name="sub_addtorneo_fromtable" class="region_SUBMIT_sub_addtorneo_fromtable btn btn-success w-100">
                    sub_addtorneo_fromtable
                </button>
            </div>
            <!-- END SUBMIT sub_addtorneo_fromtable -->
            <!-- BEGIN SUBMIT delpartidotorneo -->
            <div class="col" style="display: none">
                <button type="submit" id="sub_delpartidotorneo" name="sub_delpartidotorneo" class="region_SUBMIT_delpartidotorneo btn btn-success w-100">
                    sub_delpartidotorneo
                </button>
            </div>
            <!-- END SUBMIT delpartidotorneo -->
            <?php #region region SUBMIT sub_apostar_apuesta_rapida ?>
            <div class="col" style="display: none">
                <button type="submit" id="sub_apostar_apuesta_rapida" name="sub_apostar_apuesta_rapida" class="btn btn-success w-100">
                    sub_apostar_apuesta_rapida
                </button>
            </div>
            <?php #endregion sub_apostar_apuesta_rapida ?>

            <!-- BEGIN ROW -->
            <div class="row">
                <?php #region region SUBMIT GROUP DROPDOWN ir a ?>
                <div class="col-md-4 col-xs-12">
                    <div class="btn-group w-100">
                        <a href="#" class="btn btn-xs btn-default w-100 no-border-radious">
                            Ir a
                        </a>
                        <a href="#" class="btn btn-xs btn-default w-70px no-border-radious dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fa fa-caret-down"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php #region region SUBMIT sub_verodds ?>
                            <button type="submit" id="sub_verodds" name="sub_verodds" class="dropdown-item">
                                Ver odds
                            </button>
                            <?php #endregion SUBMIT sub_verodds ?>
                            <a href="lpartidosapuestas" class="dropdown-item">
                                Ir a apuestas
                            </a>
                            <a href="lpartidosapuestasmarcadas" class="dropdown-item">
                                Ir a apuestas marcadas
                            </a>
                            <a href="lpartidosporrevisar" class="dropdown-item">
                                Ir a por revisar
                            </a>
                            <a href="lpartidos" class="dropdown-item">
                                Ir a partidos
                            </a>
                        </ul>
                    </div>
                </div>
                <?php #endregion SUBMIT GROUP DROPDOWN ir a ?>
                <!-- sub_proxpartidoporrevisar -->
                <div class="col-md-4 col-xs-12">
                    <button type="submit" id="sub_proxpartidoporrevisar" name="sub_proxpartidoporrevisar" class="region_SUBMIT_sub_proxpartidoporrevisar btn btn-xs btn-primary w-100 no-border-radious">
                        Proximo por revisar (<?php echo $count_partidosporrevisar; ?>) (Apostado: <?php echo $count_apuestas; ?> | <?php echo formatCurrencyConSigno($sum_riesgo); ?>)
                    </button>
                </div>
                <!-- END sub_proxpartidoporrevisar -->
                <!-- sub_calcular -->
                <div class="col-md-4 col-xs-12">
                    <button type="submit" id="sub_calcular" name="sub_calcular" class="region_SUBMIT_sub_calcular btn btn-xs btn-success w-100 no-border-radious">
                        Calcular
                    </button>
                </div>
                <!-- END sub_calcular -->
            </div>
            <!-- END ROW -->
            <?php #region region info match header ?>
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious text-center" name="home" id="home" value="<?php echo @recover_var($modpartido->home) ?>" readonly onclick="copy_home()"/>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-2 col-xs-12">
                    <input type="text" name="home_fechamax" id="home_fechamax" class="form-control form-control-fh fs-12px no-border-radious text-center" value="<?php echo @recover_var($home_maxfecha) ?>"/>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-2 col-xs-12">
                    <input type="text" name="away_fechamax" id="away_fechamax" class="form-control form-control-fh fs-12px no-border-radious text-center" value="<?php echo @recover_var($away_maxfecha) ?>"/>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious text-center" name="away" id="away" value="<?php echo @recover_var($modpartido->away) ?>" readonly onclick="copy_away()"/>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-9 col-xs-12">
                    <div class="input-group">
                        <input type="text" name="pais" id="pais" class="form-control form-control-fh fs-12px no-border-radious text-center" value="<?php echo @recover_var($modpartido->pais) ?>" readonly onclick="this.focus();this.select('');"/>
                        <span class="input-group-text no-border-radious bg-primary fs-10px" data-toggle="tooltip" data-placement="bottom" title="desde la ultima actualizacion">
                            <?php echo $pais_season->dias_desde_actualizado; ?> dias
                        </span>
                        <span class="input-group-text no-border-radious bg-primary fs-10px" data-toggle="tooltip" data-placement="bottom" title="% de progreso del torneo">
                            <?php echo $pais_season->perc_progress; ?>%
                        </span>
                        <span class="input-group-text no-border-radious bg-primary fs-10px" data-toggle="tooltip" data-placement="bottom" title="# de partidos de la misma liga por revisar">
                            <?php echo $count_partidos_mismotorneo_porrevisar; ?>
                        </span>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <input type="text" name="fecha" id="fecha" class="form-control form-control-fh fs-12px no-border-radious text-center" value="<?php echo @recover_var($modpartido->fechadiashora) ?>" readonly/>
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <?php #endregion info match header ?>
            <?php #region region NAVTAB HEAD ?>
            <ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
                <li class="nav-item" onclick="tabselect(9)">
                    <a href="#default-tab-9" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 9) ? "active" : ""; ?>">
                        Torneos
                        <span class="badge  bg-primary rounded-0 fs-9px ms-1">
                            <?php echo count($partidotorneos) + 1; ?>
                        </span>
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(3)">
                    <a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">
                        Apostado
                        <span class="badge  bg-primary rounded-0 fs-9px ms-1">
                            <?php echo count($partidosapuestas); ?>
                        </span>
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(1)">
                    <a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
                        Apostar
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(6)">
                    <a href="#default-tab-6" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 6) ? "active" : ""; ?>">
                        Total goles
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(10)">
                    <a href="#default-tab-10" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 10) ? "active" : ""; ?>">
                        Goles (H)
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(11)">
                    <a href="#default-tab-11" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 11) ? "active" : ""; ?>">
                        Goles (A)
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(2)">
                    <a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
                        Total corners
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(7)">
                    <a href="#default-tab-7" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 7) ? "active" : ""; ?>">
                        Corners (H)
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(8)">
                    <a href="#default-tab-8" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 8) ? "active" : ""; ?>">
                        Corners (A)
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(12)">
                    <a href="#default-tab-12" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 12) ? "active" : ""; ?>">
                        Fixture
                    </a>
                </li>
            </ul>
            <?php #endregion NAVTAB HEAD ?>
            <div class="tab-content panel rounded-0">
                <?php #region region TAB torneos ?>
                <div class="region_NAVTAB_torneos region_NAVTAB_9 tab-pane fade <?php echo ($tabselected == 9) ? "active show" : ""; ?>" id="default-tab-9">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <!-- BEGIN text -->
                        <div class="col-md-12 col-xs-12">
                            <label for="newtorneos" class="d-flex align-items-center fs-12px">
                                Torneos:
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control text-uppercase form-control-fh fs-12px no-border-radious" name="newtorneos" id="newtorneos" value="<?php echo @recover_var($newtorneos) ?>" onclick="this.focus();this.select('');"/>
                                <!-- sub_addtorneo -->
                                <div class="input-group-btn">
                                    <button type="submit" id="sub_addtorneo" name="sub_addtorneo" class="region_SUBMIT_sub_addtorneo btn btn-xs btn-primary no-border-radious">
                                        Agregar
                                    </button>
                                </div>
                                <!-- END sub_addtorneo -->
                            </div>
                        </div>
                        <!-- END text -->
                    </div>
                    <!-- END ROW -->

                    <h5 class="mt-3 ps-1 text-info">Torneos analizados:</h5>

                    <!-- TABLE -->
                    <table class="region_TABLE_torneos table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-10px"></th>
                            <th>Torneo</th>
                            <th class="text-center">Seasons</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <tr>
                            <td></td>
                            <td><?php echo $modpartido->pais; ?></td>
                            <td class="text-center"><?php echo $modpartido->seasons; ?></td>
                        </tr>
                        <?php foreach ($partidotorneos as $partidotorneo): ?>
                            <tr>
                                <td>
                                    <i class="fa fa-trash fa-md cursor-pointer text-danger ms-1" onclick="region_JS_delpartidotorneo('<?php echo limpiar_datos($partidotorneo->id); ?>');"></i>
                                </td>
                                <td><?php echo $partidotorneo->pais->nombre; ?></td>
                                <td class="text-center"><?php echo $partidotorneo->seasons; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->

                    <h5 class="mt-3 ps-1 text-info">Torneos disponibles:</h5>

                    <!-- TABLE -->
                    <table class="region_TABLE_torneos_disponibles table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-10px"></th>
                            <th>Torneo</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php for ($i = 0; $i < count($partidotorneos_disponible); $i++): ?>
                            <tr>
                                <td>
                                    <i class="fa fa-check fa-md cursor-pointer text-success" onclick="region_JS_addtorneo_fromtable('<?php echo limpiar_datos($partidotorneos_disponible[$i]['idpais']); ?>');"></i>
                                </td>
                                <td><?php echo $partidotorneos_disponible[$i]['torneo']; ?></td>
                            </tr>
                        <?php endfor; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <?php #endregion TAB torneos ?>
                <?php #region region TAB apostado ?>
                <div class="region_NAVTAB_apostado region_NAVTAB_3 tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
                    <!-- TABLE -->
                    <table class="region_TABLE_apostado table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-10px"></th>
                            <th class="text-center">Tipo apuesta</th>
                            <th class="text-center">Win %</th>
                            <th class="text-center">Potencial</th>
                            <th class="text-center">Riesgo</th>
                            <th class="text-center">Profit</th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php foreach ($partidosapuestas as $partidoapuesta): ?>
                            <tr class="<?php echo $partidoapuesta->bgcolor; ?>">
                                <td>
                                    <i class="fa fa-pencil fa-md cursor-pointer" onclick="region_JS_editpartidoapuesta('<?php echo limpiar_datos($partidoapuesta->id); ?>');"></i>
                                </td>
                                <td><?php echo $partidoapuesta->apuestatipo->nombre . " ($partidoapuesta->valorapuesta)"; ?></td>
                                <td class="text-center"><?php echo $partidoapuesta->winprobabilityporc; ?>%</td>
                                <td class="text-center"><?php echo $partidoapuesta->potencial; ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($partidoapuesta->riesgo); ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($partidoapuesta->profit); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <?php #endregion TAB apostado ?>
                <?php #region region TAB apostar ?>
                <div class="p-2 tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
                    <!-- ROW -->
                    <div class="row">
                        <!-- BEGIN text -->
                        <div class="col-md-8 col-xs-12">
                            <label for="apuestatipo_apostar" class="d-flex align-items-center fs-12px">
                                Tipo de apuesta:
                            </label>
                            <input type="text" class="form-control form-control-fh fs-12px text-uppercase no-border-radious autocomplete" name="apuestatipo_apostar" id="apuestatipo_apostar" value="<?php echo @recover_var($apuestatipo_apostar) ?>" onclick="this.focus();this.select('');"/>
                        </div>
                        <!-- END text -->
                        <!-- BEGIN text -->
                        <div class="col-md-4 col-xs-12">
                            <label for="valorapuesta_apostar" class="d-flex align-items-center fs-12px">
                                Valor apuesta:
                            </label>
                            <input type="text" class="form-control form-control-fh fs-12px no-border-radious" name="valorapuesta_apostar" id="valorapuesta_apostar" value="<?php echo @recover_var($valorapuesta_apostar) ?>"/>
                        </div>
                        <!-- END text -->
                    </div>
                    <!-- END ROW -->
                    <!-- BEGIN ROW -->
                    <div class="row mt-3">
                        <!-- BEGIN text -->
                        <div class="col-md-6 col-xs-12">
                            <label for="riesgo_apostar" class="d-flex align-items-center fs-12px">
                                Riesgo:
                            </label>
                            <input type="text" class="form-control form-control-fh fs-12px no-border-radious" name="riesgo_apostar" id="riesgo_apostar" value="<?php echo @recover_var($riesgo_apostar) ?>"/>
                        </div>
                        <!-- END text -->
                        <!-- BEGIN text -->
                        <div class="col-md-6 col-xs-12">
                            <label for="potencial_apostar" class="d-flex align-items-center fs-12px">
                                Potencial:
                            </label>
                            <input type="text" class="form-control form-control-fh fs-12px no-border-radious" name="potencial_apostar" id="potencial_apostar" value="<?php echo @recover_var($potencial_apostar) ?>"/>
                        </div>
                        <!-- END text -->
                    </div>
                    <!-- END ROW -->
                    <!-- ROW -->
                    <div class="row mt-3">
                        <!-- sub_apostar -->
                        <div class="col-md-6 col-xs-12">
                            <button type="submit" id="sub_apostar" name="sub_apostar" class="region_SUBMIT_sub_apostar btn btn-xs btn-primary w-100 no-border-radious">
                                Agregar
                            </button>
                        </div>
                        <!-- END sub_apostar -->
                        <!-- sub_apostarprediccion -->
                        <div class="col-md-6 col-xs-12">
                            <button type="submit" id="sub_apostarprediccion" name="sub_apostarprediccion" class="region_SUBMIT_sub_apostarprediccion btn btn-xs btn-primary w-100 no-border-radious">
                                Agregar prediccion
                            </button>
                        </div>
                        <!-- END sub_apostarprediccion -->
                    </div>
                    <!-- END ROW -->
                </div>
                <?php #endregion TAB apostar ?>
                <?php #region region TAB total goles ?>
                <div class="tab-pane fade <?php echo ($tabselected == 6) ? "active show" : ""; ?>" id="default-tab-6">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <?php #region region SUBMIT_GROUP total goles masde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Total goles | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_TOTAL_GOLES; $i <= PartidoInfo::MAX_TOTAL_GOLES; $i++): ?>
                                    <button type="submit" id="sub_totalgoles_masde_<?php echo $i; ?>" name="sub_totalgoles_masde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($totalgoles_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP total goles masde ?>
                        <?php #region region SUBMIT_GROUP total goles menosde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Total goles | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_TOTAL_GOLES; $i <= PartidoInfo::MAX_TOTAL_GOLES; $i++): ?>
                                    <button type="submit" id="sub_totalgoles_menosde_<?php echo $i; ?>" name="sub_totalgoles_menosde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($totalgoles_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP total goles menosde ?>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE total goles ?>
                    <table class="region_TABLE_totalgoles table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>goles</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>goles (T)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $totalgoles_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $totalgoles_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body ?>
                        <?php for ($i = 0; $i < count($criterios); $i++): ?>
                            <tr class="<?php echo ($i == 1 || $i == 5) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center <?php echo ($numeropartidos[$i] < 12) ? "bg-danger" : ""; ?>">
                                    <?php echo $numeropartidos[$i]; ?>
                                </td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $rank_goals[$i]; ?></td>
                                <td class="text-center <?php echo $promediogoles['diff_masde'][$i]['totalbgcolor']; ?>">
                                    <?php echo $promediogoles['diff_masde'][$i]['total']; ?>
                                </td>
                                <td class="text-center">
                                    <?php echo $promediogoles['total'][$i]; ?>
                                </td>
                                <td class="text-center <?php echo $promediogoles['diff_menosde'][$i]['totalbgcolor']; ?>">
                                    <?php echo $promediogoles['diff_menosde'][$i]['total']; ?>
                                </td>
                                <td class="text-end <?php echo $infototalgoles['probabilidades'][$i]['masdebg']; ?>">
                                    <?php echo $infototalgoles['cumple'][$i]['masde']; ?> | <?php echo $infototalgoles['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infototalgoles['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infototalgoles['probabilidades'][$i]['menosde']; ?>% | <?php echo $infototalgoles['cumple'][$i]['menosde']; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body ?>
                        <?php #region region avg all ?>
                        <tr>
                            <td>AVG ALL</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-end <?php echo $infototalgoles['probabilidades']['promedio']['masdebg']; ?>">
                                <?php echo $infototalgoles['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infototalgoles['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infototalgoles['probabilidades']['promedio']['menosde']; ?>%
                            </td>
                        </tr>
                        <?php #endregion avg all ?>
                        <?php #region region avg specific ?>
                        <tr>
                            <td>AVG (H) @H & (A) @A:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-end <?php echo $infototalgoles['probabilidades']['promedio']['masdebg_important']; ?>">
                                <?php echo $infototalgoles['probabilidades']['promedio']['masde_important']; ?>%
                            </td>
                            <td class="text-start <?php echo $infototalgoles['probabilidades']['promedio']['menosdebg_important']; ?>">
                                <?php echo $infototalgoles['probabilidades']['promedio']['menosde_important']; ?>%
                            </td>
                        </tr>
                        <?php #endregion avg specific ?>
                        <?php #region region apostar ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_totalgoles_masde" name="sub_apostar_totalgoles_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_totalgoles_menosde" name="sub_apostar_totalgoles_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE total goles ?>
                </div>
                <?php #endregion TAB total goles ?>
                <?php #region region TAB goles home ?>
                <div class="tab-pane fade <?php echo ($tabselected == 10) ? "active show" : ""; ?>" id="default-tab-10">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <?php #region region SUBMIT_GROUP goles home masde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Goles Home | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_goleshome_masde_<?php echo $i; ?>" name="sub_goleshome_masde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($goleshome_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP goles home masde ?>
                        <?php #region region SUBMIT_GROUP goles home menosde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Goles Home | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_goleshome_menosde_<?php echo $i; ?>" name="sub_goleshome_menosde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($goleshome_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP goles home menosde ?>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE goles home ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>goles</th>
                            <th class="text-center">Avg<br>conceded (A)</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>goles (H)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $goleshome_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $goleshome_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body - goles home ?>
                        <?php for ($i = 0; $i < 3; $i++): ?>
                            <tr class="<?php echo ($i == 1) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center"><?php echo $numeropartidos[$i]; ?></td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $rank_goals[$i]; ?></td>
                                <td class="text-center"><?php echo $infogolesaway['conceded'][$i + 3]; ?></td>
                                <td class="text-center <?php echo $promediogoles['diff_masde'][$i]['homebgcolor']; ?>">
                                    <?php echo $promediogoles['diff_masde'][$i]['home']; ?>
                                </td>
                                <td class="text-center">
                                    <?php echo $promediogoles['home'][$i]; ?>
                                </td>
                                <td class="text-center <?php echo $promediogoles['diff_menosde'][$i]['homebgcolor']; ?>">
                                    <?php echo $promediogoles['diff_menosde'][$i]['home']; ?>
                                </td>
                                <td class="text-end <?php echo $infogoleshome['probabilidades'][$i]['masdebg']; ?>">
                                    <?php echo $infogoleshome['cumple'][$i]['masde']; ?> | <?php echo $infogoleshome['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infogoleshome['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infogoleshome['probabilidades'][$i]['menosde']; ?>% | <?php echo $infogoleshome['cumple'][$i]['menosde']; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body - goles home ?>
                        <?php #region region avg all - goles home ?>
                        <tr>
                            <td>AVG ALL:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediogoles['sumadiff']['home']['masde'], 2); ?>
                            </td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediogoles['sumadiff']['home']['menosde'], 2); ?>
                            </td>
                            <td class="text-end <?php echo $infogoleshome['probabilidades']['promedio']['masdebg']; ?>">
                                <?php echo $infogoleshome['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infogoleshome['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infogoleshome['probabilidades']['promedio']['menosde']; ?>%
                            </td>
                        </tr>
                        <?php #endregion avg all - goles home ?>
                        <?php #region region apostar - goles home ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_goleshome_masde" name="sub_apostar_goleshome_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_goleshome_menosde" name="sub_apostar_goleshome_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar - goles home ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE goles home ?>
                </div>
                <?php #endregion region TAB goles home ?>
                <?php #region region TAB goles away ?>
                <div class="tab-pane fade <?php echo ($tabselected == 11) ? "active show" : ""; ?>" id="default-tab-11">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <?php #region region SUBMIT_GROUP goles away masde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Goles Away | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_golesaway_masde_<?php echo $i; ?>" name="sub_golesaway_masde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($golesaway_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP goles away masde ?>
                        <?php #region region SUBMIT_GROUP goles away menosde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Goles Away | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_GOLES_PERTEAM; $i <= PartidoInfo::MAX_GOLES_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_golesaway_menosde_<?php echo $i; ?>" name="sub_golesaway_menosde_<?php echo $i; ?>" class="btn btn-outline-inverse no-border-radious <?php echo (($golesaway_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP goles away menosde ?>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE goles away ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>goals</th>
                            <th class="text-center">Avg<br>conceded (H)</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>goles (A)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $golesaway_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $golesaway_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body - goles away ?>
                        <?php for ($i = 3; $i < 6; $i++): ?>
                            <tr class="<?php echo ($i == 5) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center"><?php echo $numeropartidos[$i]; ?></td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $rank_goals[$i]; ?></td>
                                <td class="text-center"><?php echo $infogoleshome['conceded'][$i - 3]; ?></td>
                                <td class="text-center <?php echo $promediogoles['diff_masde'][$i]['awaybgcolor']; ?>">
                                    <?php echo $promediogoles['diff_masde'][$i]['away']; ?>
                                </td>
                                <td class="text-center">
                                    <?php echo $promediogoles['away'][$i]; ?>
                                </td>
                                <td class="text-center <?php echo $promediogoles['diff_menosde'][$i]['awaybgcolor']; ?>">
                                    <?php echo $promediogoles['diff_menosde'][$i]['away']; ?>
                                </td>
                                <td class="text-end <?php echo $infogolesaway['probabilidades'][$i]['masdebg']; ?>">
                                    <?php echo $infogolesaway['cumple'][$i]['masde']; ?> | <?php echo $infogolesaway['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infogolesaway['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infogolesaway['probabilidades'][$i]['menosde']; ?>% | <?php echo $infogolesaway['cumple'][$i]['menosde']; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body - goles away ?>
                        <?php #region region avg all - goles away ?>
                        <tr>
                            <td>AVG ALL:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediogoles['sumadiff']['away']['masde'], 2); ?>
                            </td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediogoles['sumadiff']['away']['menosde'], 2); ?>
                            </td>
                            <td class="text-end <?php echo $infogolesaway['probabilidades']['promedio']['masdebg']; ?>">
                                <?php echo $infogolesaway['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infogolesaway['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infogolesaway['probabilidades']['promedio']['menosde']; ?>%
                            </td>
                        </tr>
                        <?php #endregion avg all - goles away ?>
                        <?php #region region apostar - goles away ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_golesaway_masde" name="sub_apostar_golesaway_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_golesaway_menosde" name="sub_apostar_golesaway_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar - goles away ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE goles away ?>
                </div>
                <?php #endregion region TAB goles away ?>
                <?php #region region TAB total corners ?>
                <div class="region_NAVTAB_totalcorners tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <?php #region region SUBMIT_GROUP total corners masde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Total corners | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_TOTAL_CORNERS; $i <= PartidoInfo::MAX_TOTAL_CORNERS; $i++): ?>
                                    <button type="submit" id="sub_totalcorners_masde_<?php echo $i; ?>" name="sub_totalcorners_masde_<?php echo $i; ?>" class="region_SUBMIT_sub_totalcorners_masde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($totalcorners_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP total corners masde ?>
                        <?php #region region SUBMIT_GROUP total corners menosde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Total corners | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_TOTAL_CORNERS; $i <= PartidoInfo::MAX_TOTAL_CORNERS; $i++): ?>
                                    <button type="submit" id="sub_totalcorners_menosde_<?php echo $i; ?>" name="sub_totalcorners_menosde_<?php echo $i; ?>" class="region_SUBMIT_sub_totalcorners_menosde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($totalcorners_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP total corners menosde ?>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE total corners ?>
                    <table class="region_TABLE_totalcorners table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>corners</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>corners (T)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $totalcorners_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $totalcorners_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body ?>
                        <?php for ($i = 0; $i < count($criterios); $i++): ?>
                            <tr class="<?php echo ($i == 1 || $i == 5) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center"><?php echo $numeropartidos[$i]; ?></td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $corners[$i]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_masde'][$i]['totalbgcolor']; ?>">
                                    <?php echo $promediocorners['diff_masde'][$i]['total']; ?>
                                </td>
                                <td class="text-center"><?php echo $promediocorners['total'][$i]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_menosde'][$i]['totalbgcolor']; ?>">
                                    <?php echo $promediocorners['diff_menosde'][$i]['total']; ?>
                                </td>
                                <td class="text-end <?php echo $infototalcorners['probabilidades'][$i]['masdebg']; ?>">
                                    <?php echo $infototalcorners['cumple'][$i]['masde']; ?> | <?php echo $infototalcorners['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infototalcorners['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infototalcorners['probabilidades'][$i]['menosde']; ?>% | <?php echo $infototalcorners['cumple'][$i]['menosde']; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body ?>
                        <?php #region region avg all ?>
                        <tr class="border-top-3px">
                            <td>Promedio ALL:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['total']['masde'], 2); ?>
                            </td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['total']['menosde'], 2); ?>
                            </td>
                            <td class="text-end <?php echo $infototalcorners['probabilidades']['promedio']['masdebg']; ?>">
                                <?php if ($infototalcorners['probabilidades']['promedio']['masde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                                <?php echo $infototalcorners['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infototalcorners['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infototalcorners['probabilidades']['promedio']['menosde']; ?>%
                                <?php if ($infototalcorners['probabilidades']['promedio']['menosde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php #endregion avg all ?>
                        <?php #region region avg specific ?>
                        <tr>
                            <td>Promedio (H) @H & (A) @A:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-end <?php echo $infototalcorners['probabilidades']['promedio']['masdebg_important']; ?>">
                                <?php if ($infototalcorners['probabilidades']['promedio']['masde_important'] < $current_config->val_min_avg_specific): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                                <?php echo $infototalcorners['probabilidades']['promedio']['masde_important']; ?>%
                            </td>
                            <td class="text-start <?php echo $infototalcorners['probabilidades']['promedio']['menosdebg_important']; ?>">
                                <?php echo $infototalcorners['probabilidades']['promedio']['menosde_important']; ?>%
                                <?php if ($infototalcorners['probabilidades']['promedio']['menosde_important'] < $current_config->val_min_avg_specific): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php #endregion avg specific ?>
                        <?php #region region apostar ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_totalcorners_masde" name="sub_apostar_totalcorners_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_totalcorners_menosde" name="sub_apostar_totalcorners_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE total corners ?>
                </div>
                <?php #endregion total corners ?>
                <?php #region region TAB corners home ?>
                <div class="region_NAVTAB_cornershome region_NAVTAB_7 tab-pane fade <?php echo ($tabselected == 7) ? "active show" : ""; ?>" id="default-tab-7">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <?php #region region SUBMIT_GROUP corners home masde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Corners Home | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_cornershome_masde_<?php echo $i; ?>" name="sub_cornershome_masde_<?php echo $i; ?>" class="region_SUBMIT_sub_cornershome_masde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($cornershome_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP corners home masde ?>
                        <?php #region region SUBMIT_GROUP corners home menosde ?>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Corners Home | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_cornershome_menosde_<?php echo $i; ?>" name="sub_cornershome_menosde_<?php echo $i; ?>" class="region_SUBMIT_sub_cornershome_menosde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($cornershome_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php #endregion SUBMIT_GROUP corners home menosde ?>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE corners home ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>corners</th>
                            <th class="text-center">Avg<br>conceded (A)</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>corners (H)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $cornershome_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $cornershome_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body ?>
                        <?php for ($i = 0; $i < 3; $i++): ?>
                            <tr class="<?php echo ($i == 1) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center"><?php echo $numeropartidos[$i]; ?></td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $corners[$i]; ?></td>
                                <td class="text-center"><?php echo $infocornersaway['conceded'][$i + 3]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_masde'][$i]['homebgcolor']; ?>">
                                    <?php echo $promediocorners['diff_masde'][$i]['home']; ?>
                                </td>
                                <td class="text-center"><?php echo $promediocorners['home'][$i]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_menosde'][$i]['homebgcolor']; ?>">
                                    <?php echo $promediocorners['diff_menosde'][$i]['home']; ?>
                                </td>
                                <td class="text-end <?php echo $infocornershome['probabilidades'][$i]['masdebg']; ?>">
                                    <?php if ($i == 1 && $infocornershome['probabilidades'][$i]['masde'] < $current_config->val_min_avg_specific): ?>
                                        <i class="fa fa-circle fs-8px text-danger"></i>
                                    <?php endif; ?>
                                    <?php echo $infocornershome['cumple'][$i]['masde']; ?> | <?php echo $infocornershome['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infocornershome['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infocornershome['probabilidades'][$i]['menosde']; ?>% | <?php echo $infocornershome['cumple'][$i]['menosde']; ?>
                                    <?php if ($i == 1 && $infocornershome['probabilidades'][$i]['menosde'] < $current_config->val_min_avg_specific): ?>
                                        <i class="fa fa-circle fs-8px text-danger"></i>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body ?>
                        <?php #region region avg all ?>
                        <tr>
                            <td>AVG ALL:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['home']['masde'], 2); ?>
                            </td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['home']['menosde'], 2); ?>
                            </td>
                            <td class="text-end <?php echo $infocornershome['probabilidades']['promedio']['masdebg']; ?>">
                                <?php if ($infocornershome['probabilidades']['promedio']['masde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                                <?php echo $infocornershome['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infocornershome['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infocornershome['probabilidades']['promedio']['menosde']; ?>%
                                <?php if ($infocornershome['probabilidades']['promedio']['menosde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php #endregion avg all ?>
                        <?php #region region apostar ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_cornershome_masde" name="sub_apostar_cornershome_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_cornershome_menosde" name="sub_apostar_cornershome_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE corners home ?>
                </div>
                <?php #endregion corners home ?>
                <?php #region region TAB corners away ?>
                <div class="region_NAVTAB_cornersaway region_NAVTAB_8 tab-pane fade <?php echo ($tabselected == 8) ? "active show" : ""; ?>" id="default-tab-8">
                    <!-- BEGIN ROW -->
                    <div class="row pt-2 pe-2 ps-2">
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Corners Away | Mas de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_cornersaway_masde_<?php echo $i; ?>" name="sub_cornersaway_masde_<?php echo $i; ?>" class="region_SUBMIT_sub_cornersaway_masde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($cornersaway_masde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="col-md-6 col-xs-12">
                            <label class="d-flex align-items-center fs-12px">
                                Corners Away | Menos de:
                            </label>
                            <div class="btn-group w-100">
                                <?php for ($i = PartidoInfo::MIN_CORNERS_PERTEAM; $i <= PartidoInfo::MAX_CORNERS_PERTEAM; $i++): ?>
                                    <button type="submit" id="sub_cornersaway_menosde_<?php echo $i; ?>" name="sub_cornersaway_menosde_<?php echo $i; ?>" class="region_SUBMIT_sub_cornersaway_menosde_<?php echo $i; ?> btn btn-outline-inverse no-border-radious <?php echo (($cornersaway_menosde - 0.5) == $i) ? "active" : ""; ?>">
                                        <?php echo $i + 0.5; ?>
                                    </button>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    <!-- END ROW -->
                    <?php #region region TABLE corners away ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center w-20">Partidos</th>
                            <th class="text-center">Rank<br>posesion</th>
                            <th class="text-center">Rank<br>shots</th>
                            <th class="text-center">Rank<br>shots (target)</th>
                            <th class="text-center">Rank<br>corners</th>
                            <th class="text-center">Avg<br>conceded (H)</th>
                            <th class="text-center">DC+</th>
                            <th class="text-center">Avg<br>corners (A)</th>
                            <th class="text-center">DC-</th>
                            <th class="text-center w-150px">Mas de %<br><?php echo $cornersaway_masde; ?></th>
                            <th class="text-center w-150px">Menos de %<br><?php echo $cornersaway_menosde; ?></th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php #region region body ?>
                        <?php for ($i = 3; $i < 6; $i++): ?>
                            <tr class="<?php echo ($i == 5) ? "table-highlight" : ""; ?>">
                                <td><?php echo $criterios[$i]; ?></td>
                                <td class="text-center"><?php echo $numeropartidos[$i]; ?></td>
                                <td class="text-center"><?php echo $posession[$i]; ?></td>
                                <td class="text-center"><?php echo $shots[$i]; ?></td>
                                <td class="text-center"><?php echo $shotstarget[$i]; ?></td>
                                <td class="text-center"><?php echo $corners[$i]; ?></td>
                                <td class="text-center"><?php echo $infocornershome['conceded'][$i - 3]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_masde'][$i]['awaybgcolor']; ?>">
                                    <?php echo $promediocorners['diff_masde'][$i]['away']; ?>
                                </td>
                                <td class="text-center"><?php echo $promediocorners['away'][$i]; ?></td>
                                <td class="text-center <?php echo $promediocorners['diff_menosde'][$i]['awaybgcolor']; ?>">
                                    <?php echo $promediocorners['diff_menosde'][$i]['away']; ?>
                                </td>
                                <td class="text-end <?php echo $infocornersaway['probabilidades'][$i]['masdebg']; ?>">
                                    <?php if ($i == 5 && $infocornersaway['probabilidades'][$i]['masde'] < $current_config->val_min_avg_specific): ?>
                                        <i class="fa fa-circle fs-8px text-danger"></i>
                                    <?php endif; ?>
                                    <?php echo $infocornersaway['cumple'][$i]['masde']; ?> | <?php echo $infocornersaway['probabilidades'][$i]['masde']; ?>%
                                </td>
                                <td class="text-start <?php echo $infocornersaway['probabilidades'][$i]['menosdebg']; ?>">
                                    <?php echo $infocornersaway['probabilidades'][$i]['menosde']; ?>% | <?php echo $infocornersaway['cumple'][$i]['menosde']; ?>
                                    <?php if ($i == 5 && $infocornersaway['probabilidades'][$i]['menosde'] < $current_config->val_min_avg_specific): ?>
                                        <i class="fa fa-circle fs-8px text-danger"></i>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion body ?>
                        <?php #region region avg all ?>
                        <tr>
                            <td>AVG ALL:</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['away']['masde'], 2); ?>
                            </td>
                            <td></td>
                            <td class="text-center">
                                <?php echo round($promediocorners['sumadiff']['away']['menosde'], 2); ?>
                            </td>
                            <td class="text-end <?php echo $infocornersaway['probabilidades']['promedio']['masdebg']; ?>">
                                <?php if ($infocornersaway['probabilidades']['promedio']['masde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                                <?php echo $infocornersaway['probabilidades']['promedio']['masde']; ?>%
                            </td>
                            <td class="text-start <?php echo $infocornersaway['probabilidades']['promedio']['menosdebg']; ?>">
                                <?php echo $infocornersaway['probabilidades']['promedio']['menosde']; ?>%
                                <?php if ($infocornersaway['probabilidades']['promedio']['menosde'] < $current_config->val_min_avg_all): ?>
                                    <i class="fa fa-circle fs-8px text-danger"></i>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php #endregion avg all ?>
                        <?php #region region apostar ?>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <button type="submit" id="sub_apostar_cornersaway_masde" name="sub_apostar_cornersaway_masde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                            <td>
                                <button type="submit" id="sub_apostar_cornersaway_menosde" name="sub_apostar_cornersaway_menosde" class="p-0 btn btn-link w-100 text-white-100">
                                    Apostar
                                </button>
                            </td>
                        </tr>
                        <?php #endregion apostar ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE corners away ?>
                </div>
                <?php #endregion TAB corners away ?>
                <?php #region region TAB fixture ?>
                <div class="tab-pane fade <?php echo ($tabselected == 12) ? "active show" : ""; ?>" id="default-tab-12">
                    <?php #region region TABLE comparacion ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center">Home</th>
                            <th class="text-center">Away</th>
                        </tr>
                        <tbody class="fs-14px">
                        <?php #region region ARRAY comparacion ?>
                        <?php for ($i = 0; $i < count($fixture); $i++): ?>
                            <tr>
                                <td><?php echo $fixture[$i]['criterio']; ?></td>
                                <td class="text-center"><?php echo $fixture[$i]['home']; ?></td>
                                <td class="text-center"><?php echo $fixture[$i]['away']; ?></td>
                            </tr>
                        <?php endfor; ?>
                        <?php #endregion array comparacion ?>
                        </tbody>
                    </table>
                    <?php #endregion table comparacion ?>
                </div>
                <?php #endregion region TAB fixture ?>
            </div>
            <!-- END navtab -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS tooltip ?>
<script type="text/javascript">
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>
<?php #endregion JS tooltip ?>
<!-- BEGIN JS tabselect -->
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<?php #endregion JS tabselect ?>
<!-- END JS tabselect -->
<!-- BEGIN JS autocomplete -->
<script type="text/javascript">
  $("#apuestatipo_apostar").autocomplete({
    source: [
        <?php foreach ($apuestastipos as $apuestatipo): ?>
            "<?php echo $apuestatipo->nombre; ?>",     
        <?php endforeach; ?>        
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("apuestatipo_apostar");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_apostar").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("valorapuesta_apostar");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_apostar").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("potencial_apostar");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_apostar").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit_riesgo_apostar -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("riesgo_apostar");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_apostar").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("cornershome_masde");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_calcular").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("cornershome_menosde");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_calcular").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("cornersaway_masde");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_calcular").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("cornersaway_menosde");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_calcular").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("newtorneos");

    input.addEventListener("keypress", function(event) {

    if (event.key === "Enter") {
        event.preventDefault();
        document.getElementById("sub_addtorneo").click();
    }
    });
</script>
<!-- END JS pressentersubmit -->
<!-- BEGIN JS autocomplete -->
<script id="region_JS_autocomplete_newtorneos" type="text/javascript">
  $("#newtorneos").autocomplete({
    source: [
        <?php foreach ($paises as $pais): ?>
            "<?php echo $pais->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<!-- BEGIN region_JS_editpartidoapuesta -->
<script type="text/javascript">
    function region_JS_editpartidoapuesta($id) {
        const selidpartidoapuesta = document.getElementById('selidpartidoapuesta');
        selidpartidoapuesta.value = $id;

        document.getElementById('sub_editpartidoapuesta').click();
    }
</script>
<!-- END JS_editpartidoapuesta -->
<?php #region region JS copy_home ?>
<script type="text/javascript">
    function copy_home() {
        // Get the text field
        var copyText = document.getElementById("home");
        
        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        navigator.clipboard.writeText(copyText.value);

        // Alert the copied text
        // alert("Copied the text: " + copyText.value);
    }
</script>
<?php #endregion JS copy_home ?>
<?php #region region JS copy_away ?>
<script type="text/javascript">
    function copy_away() {
        // Get the text field
        var copyText = document.getElementById("away");

        // Select the text field
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text inside the text field
        navigator.clipboard.writeText(copyText.value);

        // Alert the copied text
        // alert("Copied the text: " + copyText.value);
    }
</script>
<?php #endregion JS copy_away ?>
<!-- BEGIN region_JS_addtorneo_fromtable -->
<script type="text/javascript">
    function region_JS_addtorneo_fromtable($id) {
        const selidpais = document.getElementById('selidpais');
        selidpais.value = $id;

        document.getElementById('sub_addtorneo_fromtable').click();
    }
</script>
<!-- END JS_addtorneo_fromtable -->
<!-- BEGIN region_JS_delpartidotorneo -->
<script type="text/javascript">
    function region_JS_delpartidotorneo($id) {
        const selidpartidotorneo = document.getElementById('selidpartidotorneo');
        selidpartidotorneo.value = $id;

        document.getElementById('sub_delpartidotorneo').click();
    }
</script>
<!-- END JS_delpartidotorneo -->
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: '<?php echo $success_text; ?>',
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: '<?php echo $error_text; ?>',
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS apostar_apuesta_rapida ?>
<script type="text/javascript">
    function apostar_apuesta_rapida($apuesta_tipo_id,$valor_apuesta) {
        const apuesta_tipo_id_apuesta_rapida = document.getElementById('apuesta_tipo_id_apuesta_rapida');
        apuesta_tipo_id_apuesta_rapida.value = $apuesta_tipo_id;
        const valor_apuesta_apuesta_rapida = document.getElementById('valor_apuesta_apuesta_rapida');
        valor_apuesta_apuesta_rapida.value = $valor_apuesta;

        document.getElementById('sub_apostar_apuesta_rapida').click();
    }
</script>
<?php #endregion js apostar_apuesta_rapida ?>
<?php #endregion JS ?>

</body>
</html>